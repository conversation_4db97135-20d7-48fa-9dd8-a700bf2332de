# uint32_t类型支持

本文档介绍C语言编译器对`uint32_t`类型的完整支持。

## 概述

编译器现在完全支持`uint32_t`类型，这对于处理时间戳、大数值和无符号运算特别重要。在VM环境中，时间相关的系统调用都返回`uint32_t`类型。

## 支持的功能

### 1. 基本类型声明

```c
uint32_t main() {
    register uint32_t timestamp = 0;
    register uint32_t counter = 100;
    uint32_t local_var = 0xFFFFFFFF;  // 最大值
    
    return timestamp + counter;
}
```

### 2. 函数返回类型

```c
uint32_t get_current_time() {
    return get_time();  // VM系统调用返回uint32_t
}

uint32_t main() {
    register uint32_t time = get_current_time();
    return time;
}
```

### 3. 大数值常量

```c
uint32_t main() {
    register uint32_t max_value = 0xFFFFFFFF;    // 4294967295
    register uint32_t large_hex = 0xDEADBEEF;    // 十六进制
    register uint32_t large_dec = 4000000000;    // 十进制
    
    return max_value + large_hex + large_dec;
}
```

### 4. 位操作

```c
uint32_t main() {
    register uint32_t value = 0x12345678;
    register uint32_t mask = 0xFF00FF00;
    
    // 位运算
    register uint32_t and_result = value & mask;
    register uint32_t or_result = value | mask;
    register uint32_t xor_result = value ^ mask;
    
    // 移位运算
    register uint32_t left_shift = value << 4;
    register uint32_t right_shift = value >> 4;
    
    return and_result + or_result + xor_result;
}
```

### 5. 时间处理

```c
uint32_t main() {
    register uint32_t current_time = get_time();
    register uint32_t msg_time_0 = get_msg_time(0);
    register uint32_t msg_time_1 = get_msg_time(1);
    
    // 计算时间差
    register uint32_t diff1 = 0;
    register uint32_t diff2 = 0;
    
    if (current_time > msg_time_0) {
        diff1 = current_time - msg_time_0;
    } else {
        diff1 = msg_time_0 - current_time;
    }
    
    if (current_time > msg_time_1) {
        diff2 = current_time - msg_time_1;
    } else {
        diff2 = msg_time_1 - current_time;
    }
    
    // 返回较小的时间差
    if (diff1 < diff2) {
        return diff1;
    } else {
        return diff2;
    }
}
```

### 6. 溢出处理

```c
uint32_t main() {
    register uint32_t max_val = 0xFFFFFFFF;
    register uint32_t large_val = 0x80000000;
    
    // 测试加法溢出 (在32位系统中会自动回绕)
    register uint32_t sum = max_val + 1;      // 结果为 0
    register uint32_t product = large_val * 2; // 结果为 0
    
    // 检查溢出结果
    if (sum == 0 && product == 0) {
        return 1;  // 溢出检测成功
    } else {
        return 0;  // 溢出检测失败
    }
}
```

## VM系统调用集成

所有时间相关的VM系统调用都返回`uint32_t`类型：

```c
uint32_t main() {
    register uint32_t current_time = get_time();        // 当前时间戳
    register uint32_t msg_time = get_msg_time(0);       // 消息时间戳
    register uint32_t event_val = get_event(42);        // 事件值 (转换为uint32_t)
    
    // 计算时间相关的逻辑
    register uint32_t timeout = 5000;  // 5秒超时
    
    if (current_time > msg_time + timeout) {
        return 1;  // 超时
    } else {
        return 0;  // 正常
    }
}
```

## 与int类型的混合使用

在VM中，`int`和`uint32_t`都是32位值，可以混合使用：

```c
uint32_t main() {
    register int signed_val = -1;           // 0xFFFFFFFF
    register uint32_t unsigned_val = 0xFFFFFFFF;
    
    // 在VM中，这两个值的位模式相同
    register uint32_t result = 0;
    
    if (signed_val == -1) {
        result = result + 100;
    }
    
    if (unsigned_val == 0xFFFFFFFF) {
        result = result + 200;
    }
    
    return result;  // 返回 300
}
```

## 常量折叠优化

编译器对`uint32_t`常量也进行优化：

```c
uint32_t main() {
    // 这些常量表达式会在编译时计算
    register uint32_t calc1 = 0x1000 + 0x2000;     // 优化为 0x3000
    register uint32_t calc2 = 1000 * 1000;         // 优化为 1000000
    register uint32_t calc3 = 0xFFFF << 16;        // 优化为 0xFFFF0000
    
    return calc1 + calc2 + calc3;
}
```

## 编译示例

```python
from c_to_vm_compiler import Compiler

c_code = """
uint32_t main() {
    register uint32_t timestamp = get_time();
    register uint32_t large_value = 0xFFFFFFFF;
    register uint32_t mask = 0x80000000;
    
    register uint32_t masked = large_value & mask;
    
    if (masked != 0) {
        timestamp = timestamp + 1000;
    }
    
    return timestamp;
}
"""

compiler = Compiler(enable_optimization=True, show_ast=True)
bytecode = compiler.compile(c_code)

print("编译成功!")
compiler.print_bytecode_hex(bytecode)
```

## 测试验证

运行以下测试来验证`uint32_t`支持：

```bash
# 基本功能测试
python applications/test_uint32_individual.py

# 完整示例
python applications/compiler_examples.py

# 单个测试
python applications/test_single_uint32.py
```

## 注意事项

1. **位模式兼容**: 在VM中，`int`和`uint32_t`具有相同的32位表示
2. **溢出行为**: 遵循32位无符号整数的回绕规则
3. **系统调用**: 时间相关的VM系统调用返回`uint32_t`
4. **优化**: 常量折叠和代数简化同样适用于`uint32_t`
5. **语义**: 主要用于编译时类型检查和代码可读性

## 应用场景

`uint32_t`类型特别适用于：

- **时间戳处理**: 毫秒级时间戳
- **大数值计算**: 超过int范围的正数
- **位掩码操作**: 32位标志位处理
- **硬件寄存器**: 外设寄存器值
- **协议字段**: 网络协议中的32位字段
- **计数器**: 大范围计数器值

这个增强使编译器能够更好地处理嵌入式系统中常见的时间和数值处理需求。
