#!/usr/bin/env python3
"""
逐个测试uint32_t功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from c_to_vm_compiler import Compiler

def test_basic():
    print("测试1: 基本uint32_t")
    c_code = """
    uint32_t main() {
        register uint32_t a = 0xFFFFFFFF;
        register uint32_t b = 0x80000000;
        register uint32_t c = 0x00000001;
        
        return a + b + c;
    }
    """
    
    try:
        compiler = Compiler(enable_optimization=True, show_ast=False)
        bytecode = compiler.compile(c_code)
        print("✅ 基本测试通过")
    except Exception as e:
        print(f"❌ 基本测试失败: {e}")

def test_time_ops():
    print("测试2: 时间操作")
    c_code = """
    uint32_t main() {
        register uint32_t current_time = get_time();
        register uint32_t msg_time_0 = get_msg_time(0);
        
        register uint32_t diff = 0;
        if (current_time > msg_time_0) {
            diff = current_time - msg_time_0;
        } else {
            diff = msg_time_0 - current_time;
        }
        
        return diff;
    }
    """
    
    try:
        compiler = Compiler(enable_optimization=True, show_ast=False)
        bytecode = compiler.compile(c_code)
        print("✅ 时间操作测试通过")
    except Exception as e:
        print(f"❌ 时间操作测试失败: {e}")

def test_bit_ops():
    print("测试3: 位操作")
    c_code = """
    uint32_t main() {
        register uint32_t value = 0x12345678;
        register uint32_t mask = 0xFF00FF00;
        
        register uint32_t and_result = value & mask;
        register uint32_t or_result = value | mask;
        register uint32_t xor_result = value ^ mask;
        
        return and_result + or_result + xor_result;
    }
    """
    
    try:
        compiler = Compiler(enable_optimization=True, show_ast=False)
        bytecode = compiler.compile(c_code)
        print("✅ 位操作测试通过")
    except Exception as e:
        print(f"❌ 位操作测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic()
    test_time_ops()
    test_bit_ops()
