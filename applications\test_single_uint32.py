#!/usr/bin/env python3
"""
单个uint32_t测试
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from c_to_vm_compiler import Compiler

def main():
    c_code = """
    uint32_t main() {
        register uint32_t a = 0xFFFFFFFF;
        register uint32_t b = 0x80000000;
        register uint32_t c = 0x00000001;
        
        return a + b + c;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    try:
        compiler = Compiler(enable_optimization=True, show_ast=True)
        bytecode = compiler.compile(c_code)
        print("✅ 编译成功!")
        compiler.print_bytecode_hex(bytecode)
    except Exception as e:
        print(f"❌ 编译失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
