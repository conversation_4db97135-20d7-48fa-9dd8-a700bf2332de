#!/usr/bin/env python3
"""
调试位运算解析问题
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from c_to_vm_compiler import Compiler

def test_simple_bitwise():
    """简单位运算测试"""
    print("🔧 简单位运算测试")
    
    c_code = """
    int main() {
        register int a = 5;
        register int b = 3;
        register int result = a & b;
        return result;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    try:
        compiler = Compiler(enable_optimization=False, show_ast=True)
        bytecode = compiler.compile(c_code)
        print("✅ 编译成功!")
        compiler.print_bytecode_hex(bytecode)
    except Exception as e:
        print(f"❌ 编译失败: {e}")
        import traceback
        traceback.print_exc()

def test_hex_constants():
    """十六进制常量测试"""
    print("\n🔢 十六进制常量测试")
    
    c_code = """
    int main() {
        register int a = 0xFF;
        register int b = 0x0F;
        return a + b;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    try:
        compiler = Compiler(enable_optimization=False, show_ast=True)
        bytecode = compiler.compile(c_code)
        print("✅ 编译成功!")
        compiler.print_bytecode_hex(bytecode)
    except Exception as e:
        print(f"❌ 编译失败: {e}")
        import traceback
        traceback.print_exc()

def test_uint32_simple():
    """简单uint32_t测试"""
    print("\n📝 简单uint32_t测试")
    
    c_code = """
    uint32_t main() {
        register uint32_t a = 100;
        register uint32_t b = 200;
        return a + b;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    try:
        compiler = Compiler(enable_optimization=False, show_ast=True)
        bytecode = compiler.compile(c_code)
        print("✅ 编译成功!")
        compiler.print_bytecode_hex(bytecode)
    except Exception as e:
        print(f"❌ 编译失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_bitwise()
    test_hex_constants()
    test_uint32_simple()
