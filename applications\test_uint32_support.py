#!/usr/bin/env python3
"""
uint32_t类型支持测试

测试编译器对uint32_t类型的完整支持
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from c_to_vm_compiler import Compiler

def test_uint32_basic():
    """基本uint32_t类型测试"""
    print("🧪 基本uint32_t类型测试")
    print("=" * 30)
    
    c_code = """
    uint32_t main() {
        register uint32_t a = 0xFFFFFFFF;  // 最大值
        register uint32_t b = 0x80000000;  // 符号位
        register uint32_t c = 0x00000001;  // 最小正值
        
        return a + b + c;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=True)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def test_uint32_time_operations():
    """uint32_t时间操作测试"""
    print("⏰ uint32_t时间操作测试")
    print("=" * 30)
    
    c_code = """
    uint32_t main() {
        register uint32_t current_time = get_time();
        register uint32_t msg_time_0 = get_msg_time(0);
        register uint32_t msg_time_1 = get_msg_time(1);
        
        // 计算时间差
        register uint32_t diff1 = 0;
        register uint32_t diff2 = 0;
        
        if (current_time > msg_time_0) {
            diff1 = current_time - msg_time_0;
        } else {
            diff1 = msg_time_0 - current_time;
        }
        
        if (current_time > msg_time_1) {
            diff2 = current_time - msg_time_1;
        } else {
            diff2 = msg_time_1 - current_time;
        }
        
        // 返回较小的时间差
        if (diff1 < diff2) {
            return diff1;
        } else {
            return diff2;
        }
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=False)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def test_uint32_bit_operations():
    """uint32_t位操作测试"""
    print("🔧 uint32_t位操作测试")
    print("=" * 30)
    
    c_code = """
    uint32_t main() {
        register uint32_t value = 0x12345678;
        register uint32_t mask = 0xFF00FF00;
        
        // 位操作
        register uint32_t and_result = value & mask;
        register uint32_t or_result = value | mask;
        register uint32_t xor_result = value ^ mask;
        
        // 移位操作
        register uint32_t left_shift = value << 4;
        register uint32_t right_shift = value >> 4;
        
        // 组合结果
        register uint32_t result = and_result + or_result;
        result = result + xor_result + left_shift + right_shift;
        
        return result;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=False)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def test_uint32_overflow_handling():
    """uint32_t溢出处理测试"""
    print("⚠️  uint32_t溢出处理测试")
    print("=" * 30)
    
    c_code = """
    uint32_t main() {
        register uint32_t max_val = 0xFFFFFFFF;
        register uint32_t large_val = 0x80000000;
        
        // 测试加法溢出
        register uint32_t sum = max_val + 1;  // 应该溢出到0
        
        // 测试乘法溢出
        register uint32_t product = large_val * 2;  // 应该溢出到0
        
        // 检查溢出结果
        if (sum == 0 && product == 0) {
            return 1;  // 溢出检测成功
        } else {
            return 0;  // 溢出检测失败
        }
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=False)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def test_mixed_types():
    """混合类型测试"""
    print("🔄 混合类型测试")
    print("=" * 30)
    
    c_code = """
    uint32_t main() {
        register int signed_val = -1;
        register uint32_t unsigned_val = 0xFFFFFFFF;
        
        // 注意：这里实际上两个值在32位表示中是相同的
        // 但语义上不同（有符号 vs 无符号）
        
        register uint32_t result = 0;
        
        // 在VM中，所有值都是32位，所以这些操作是等价的
        if (signed_val == -1) {
            result = result + 100;
        }
        
        if (unsigned_val == 0xFFFFFFFF) {
            result = result + 200;
        }
        
        return result;  // 应该返回300
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=False)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    print()

def test_uint32_constants():
    """uint32_t常量测试"""
    print("🔢 uint32_t常量测试")
    print("=" * 30)
    
    c_code = """
    uint32_t main() {
        // 测试各种常量格式
        register uint32_t hex_val = 0xDEADBEEF;
        register uint32_t dec_val = 4294967295;  // 0xFFFFFFFF
        register uint32_t small_val = 42;
        
        // 常量折叠测试
        register uint32_t calc_val = 0x1000 + 0x2000 + 0x3000;  // 应该被优化为0x6000
        
        return hex_val + dec_val + small_val + calc_val;
    }
    """
    
    print("C代码:")
    print(c_code)
    
    compiler = Compiler(enable_optimization=True, show_ast=False)
    bytecode = compiler.compile(c_code)
    
    print("字节码:")
    compiler.print_bytecode_hex(bytecode)
    
    # 显示优化报告
    optimizations = compiler.get_optimization_report()
    if optimizations:
        print("应用的优化:")
        for opt in optimizations:
            print(f"  • {opt}")
    print()

def main():
    print("🚀 uint32_t类型支持测试套件")
    print("=" * 50)
    print()
    
    try:
        test_uint32_basic()
        test_uint32_time_operations()
        test_uint32_bit_operations()
        test_uint32_overflow_handling()
        test_mixed_types()
        test_uint32_constants()
        
        print("🎉 所有uint32_t测试完成!")
        print("\n📋 uint32_t支持特性:")
        print("  ✅ 基本uint32_t变量声明")
        print("  ✅ register uint32_t寄存器变量")
        print("  ✅ uint32_t函数返回类型")
        print("  ✅ 十六进制常量支持 (0xFFFFFFFF)")
        print("  ✅ 大数值常量支持 (4294967295)")
        print("  ✅ 位操作 (&, |, ^, <<, >>)")
        print("  ✅ 算术操作 (+, -, *, /, %)")
        print("  ✅ 比较操作 (==, !=, <, >, <=, >=)")
        print("  ✅ VM系统调用集成")
        print("  ✅ 常量折叠优化")
        print("  ✅ 与int类型混合使用")
        
        print("\n⚠️  注意事项:")
        print("  • VM内部所有值都是32位，int和uint32_t在运行时等价")
        print("  • 类型主要用于编译时语义检查和代码可读性")
        print("  • 溢出行为遵循32位无符号整数规则")
        print("  • 时间相关的VM系统调用返回uint32_t类型")
        
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
